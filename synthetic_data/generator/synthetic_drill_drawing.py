#生成平面图，并生成对应的json文件，对虚线和大钻孔进行标注
from pathlib import Path
import numpy as np
import cv2
import json
from typing import List, Tuple, Union
from loguru import logger
import random
import string
import math
from PIL import Image, ImageDraw
from meta_type import Point, BoundingBox, LineType, CircleFillPattern, YoloLabelType
from settings import DATASETS
from synthetic_data.generator.drawing_generator import DrawingElemGenerator
from datasets_generator.yolo_base import YOLODatasetGenerator
from utils.random_utils import RandomTextGenerator, random_pos_neg, RandomGroupGenerator


class DrawingDatasetGenerator(YOLODatasetGenerator, DrawingElemGenerator):

    def __init__(self, image_size: Tuple[int, int] = (1960, 1080), output_dir: Union[str, Path] = DATASETS,
                 line_detect_type='pinhole'):
        super().__init__(
            classes=[label.value for label in YoloLabelType],
            output_dir=output_dir,
            image_size=image_size
        )
        #存储虚线相关数据，初始化为空字典
        self.dotted_lines = {}
        self.line_detect_type = line_detect_type
         # 添加用于存储JSON标注的属性
        self.json_annotations = {}

    #将目标检测的标注信息转换为YOLO格式
    def transform_annotation(self, label, bbox_xyxy: Tuple[float, float, float, float], image_size=None) -> str:
        if image_size is None:
            image_size = self.image_size
        return super().transform_annotation(label, bbox_xyxy, image_size)

    def generate_single_image(self, img_id, is_train=True, **kwargs):
        # 生成核心区域
        (start_x, start_y), width, height, angle, core_corners = self._generate_core_region()

        # 生成干扰元素
        self.generate_noise(core_corners)

        # 生成右下角表格
        # merge_cells = [
        #     (0, 0, 1, 6),
        #     (2, 1, 2, 7),
        #     (3, 1, 3, 5),
        # ]
        #self.generate_table(merged_cells=merge_cells, margin=10)

        # 生成布局
        annotations, dotted_lines = self.generate_layout((start_x, start_y), width, height, angle, core_corners)

        # 保存图片到统一目录
        image_path = Path(self.output_dir) / f"{img_id:06d}.png"
        self.image.save(image_path)

        # 保存标注到统一目录
        label_path = Path(self.output_dir) / f"{img_id:06d}.txt"
        with open(label_path, 'w') as f:
            f.write('\n'.join(annotations))

        # 生成并保存JSON标注文件到统一目录
        json_data = self.generate_json_annotations(img_id, dotted_lines, self.core_drill_holes)
        json_path = Path(self.output_dir) / f"{img_id:06d}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)

        # 清空等待生成下一张图片
        self.clear()

        return len(annotations) + len(dotted_lines)

    def generate_dataset(self, num_train=1000, num_val=200):
        """生成完整数据集"""
        # 合并训练和验证集的数量
        total_num = num_train + num_val
        
        # 创建输出目录
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)
        
        # 生成所有图片
        for i in range(total_num):
            self.generate_single_image(i)
            
        logger.info(f"数据集已生成，共 {total_num} 张图片")

    def _generate_rect_region(self, padding):
        # Generate 4 points for the main rectangle
        angle = random.uniform(0, np.pi / 4)
        width = random.randint(500, 1000)
        height = random.randint(400, 800)
        start_x = random.randint(padding, self.image_size[0] - width - padding)
        start_y = random.randint(padding, self.image_size[1] - height - padding)

        # Calculate rectangle corners
        corners = [
            Point(start_x, start_y),
            Point(start_x + width * np.cos(angle), start_y + width * np.sin(angle)),
            Point(start_x + width * np.cos(angle) - height * np.sin(angle),
                  start_y + width * np.sin(angle) + height * np.cos(angle)),
            Point(start_x - height * np.sin(angle), start_y + height * np.cos(angle))
        ]
        return (start_x, start_y), width, height, angle, corners

    def _generate_core_region(self, padding: int = 100) -> Tuple[Tuple[int, int], int, int, float, List[Point]]:
        # 如果存在顶点超出边界，则重新生成
        while True:
            (start_x, start_y), width, height, angle, corners = self._generate_rect_region(padding)
            if all(
                    padding <= corner.x <= self.image_size[0] - padding and padding <= corner.y <= self.image_size[
                        1] - padding
                    for corner in corners
            ):
                break
        return (start_x, start_y), width, height, angle, corners

    def generate_layout(self, point, width, height, angle, corners: List[Point]) -> Tuple[
        List[str], List[Tuple[Point, Point]]]:
        """Generate a random layout with points and connections."""
        start_x, start_y = point
        annotations = []
        dotted_lines = []
        # 存储钻孔信息用于JSON标注
        self.core_drill_holes = []

        # 绘制核心区域边框
        self.draw.polygon([(corner.x, corner.y) for corner in corners], outline=(0, 0, 0), fill=(255, 255, 255))
        dis_threshold = min(corners[0].distance_to(corners[1]), corners[1].distance_to(corners[2])) * 0.5

        # 生成核心钻孔
        num_points = random.randint(4, 6)
        core_points = []
        normal_points = []
        min_spacing = dis_threshold  # 最小钻孔间距
        max_attempts = 100  # 最大尝试次数

        # 生成钻孔点
        for _ in range(num_points):
            attempts = 0
            while attempts < max_attempts:
                # 在矩形内生成随机点
                u = random.random()
                v = random.random()
                point = Point(
                    start_x + u * width * np.cos(angle) - v * height * np.sin(angle),
                    start_y + u * width * np.sin(angle) + v * height * np.cos(angle)
                )

                # 检查与现有点的间距
                spacing_ok = True
                for existing_point in core_points:
                    distance = point.distance_to(existing_point)
                    if distance < min_spacing:
                        spacing_ok = False
                        break

                if spacing_ok:
                    core_points.append(point)
                    break

                attempts += 1

        # 绘制核心区域角点钻孔
        for corner in corners:
            # 在角点位置绘制钻孔
            normal_points.append(corner)
            annotations.extend(self.draw_normal_point(corner, len(normal_points)))

        # 绘制核心区域边缘钻孔
        for idx in range(len(corners)):
            p1 = corners[idx % len(corners)]
            p2 = corners[(idx + 1) % len(corners)]
            marker_points = self._generate_normal_points_by_line(p1, p2)

            for marker_point in marker_points:
                normal_points.append(marker_point)
                annotations.extend(self.draw_normal_point(marker_point, len(normal_points)))

        # 绘制钻孔和连接线
        for i, point in enumerate(core_points):
            # 绘制核心钻孔
            drill_annotations, drill_info = self.draw_core_drill_holes(point)
            annotations.extend(drill_annotations)
            self.core_drill_holes.append(drill_info)

            # 连接钻孔
            profile_line_cnt = 0
            roman_numbers = ["I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X",
                             "XI", "XII", "XIII", "XIV", "XV", "XVI", "XVII", "XVIII", "XIX", "XX"]
            for j in range(i + 1, len(core_points)):
                if random.random() < 0.25:
                    profile_line_cnt += 1
                    dotted_lines.append((point, core_points[j]))
                    annotations.extend(self.draw_profile_line(
                        YoloLabelType.CORE_PN, point, core_points[j], roman_numbers[profile_line_cnt], extension=True))

                    # 计算线段上的标记点
                    marker_points = self._generate_normal_points_by_line(point, core_points[j])

                    # 为每个标记点添加圆形和编号
                    for marker_point in marker_points:
                        normal_points.append(marker_point)
                        annotations.extend(self.draw_normal_point(marker_point, len(normal_points)))

        # 随机选择几对 normal_point 进行连接
        profile_line_cnt = 0
        if len(normal_points) >= 6:
            # 创建所有可能的点对组合
            all_pairs = []
            for i in range(len(normal_points)):
                for j in range(i + 1, len(normal_points)):
                    all_pairs.append((normal_points[i], normal_points[j]))

            # 随机选择最多5对不重复的点对
            selected_pairs = random.sample(all_pairs, min(5, len(all_pairs)))

            # 绘制选中的点对
            for p1, p2 in selected_pairs:
                profile_line_cnt += 1
                dotted_lines.append((p1, p2))
                annotations.extend(self.draw_profile_line(YoloLabelType.NORM_PN, p1, p2, profile_line_cnt, font_scale=1,
                                                          extension=True))

        # 绘制核心区域的角点标注
        annotations.extend(self.generate_corners_annotations(corners))

        return annotations, dotted_lines

    def draw_core_drill_holes(self, point: Point) -> Tuple[List[str], dict]:
        annotations = []
        # 绘制钻孔
        core_point_radius = random.randint(23, 25)
        fill_pattern = random.choice(
            [pattern.value for pattern in CircleFillPattern if pattern != CircleFillPattern.NONE])
        self.draw_circle(point, core_point_radius, fill_pattern)

        # 添加钻孔标识
        letter_num = RandomTextGenerator.generate_letter_number(b=9)
        upper_text, down_text = RandomGroupGenerator.generate_drawing_text_pair('meter_value')

        # 计算标识位置（在钻孔右侧）
        text_x = point.x + (core_point_radius + 15) * random_pos_neg()
        if random_pos_neg() > 0:
            text_y = point.y + core_point_radius + 15
        else:
            text_y = point.y - core_point_radius - 30

        # 确定 bbox
        bbox = BoundingBox(
            point.x - core_point_radius - self.bbox_padding,
            point.y - core_point_radius - self.bbox_padding,
            point.x + core_point_radius + self.bbox_padding,
            point.y + core_point_radius + self.bbox_padding
        )
        annotations.append(self.transform_annotation(YoloLabelType.CORE_DH, bbox.xyxy))

        # 绘制钻孔标识
        annotations.extend(self.draw_drill_hole_label(
            Point(text_x, text_y),
            letter_num,
            upper_text,
            down_text,
            font_scale=0.7
        ))

        # 返回钻孔信息用于JSON标注
        drill_info = {
            "center": (point.x, point.y),
            "radius": core_point_radius,
            "bbox": [
                point.x - core_point_radius,
                point.y - core_point_radius,
                point.x + core_point_radius,
                point.y + core_point_radius
            ]
        }

        return annotations, drill_info

    def draw_profile_line(self, label, point1, point2, profile_name, font_scale=0.7, extension=False) -> List[str]:
        annotations = []
        if extension:
            # 计算方向向量
            dx = point2.x - point1.x
            dy = point2.y - point1.y
            length = math.sqrt(dx * dx + dy * dy)
            dx = dx / length
            dy = dy / length

            # 延长距离
            extend_length = 50

            # 计算延长后的端点
            extended_start = Point(
                point1.x - dx * extend_length,
                point1.y - dy * extend_length
            )
            extended_end = Point(
                point2.x + dx * extend_length,
                point2.y + dy * extend_length
            )
        else:
            extended_start = point1
            extended_end = point2

        # 绘制连接线
        if random.random() < 0.8:
            # 绘制延长后的连接线
            self.draw_line(extended_start, extended_end, line_type=LineType.LSS_DASHED)
        else:
            # 绘制延长后的连接线
            self.draw_line(extended_start, extended_end, line_type=LineType.LINE)

        # 在延长后的端点添加文本标注
        start_text = f'{profile_name}'
        end_text = f'{profile_name}\''

        # 计算文本位置（在端点外侧）
        start_text_x = extended_start.x - dx * 20
        start_text_y = extended_start.y - dy * 20
        end_text_x = extended_end.x + dx * 20
        end_text_y = extended_end.y + dy * 20

        # 绘制文本并获取它们的边界框
        start_text_obj = self.draw_text(start_text, Point(start_text_x, start_text_y), 0, font_scale=font_scale,
                                        is_english='roman')
        end_text_obj = self.draw_text(end_text, Point(end_text_x, end_text_y), 0, font_scale=font_scale,
                                      is_english='roman')

        # 合并两个文本的边界框
        start_bbox = BoundingBox(
            start_text_obj.bbox.x1 - self.bbox_padding,
            start_text_obj.bbox.y1 - self.bbox_padding,
            start_text_obj.bbox.x2 + self.bbox_padding,
            start_text_obj.bbox.y2 + self.bbox_padding
        )
        end_bbox = BoundingBox(
            end_text_obj.bbox.x1 - self.bbox_padding,
            end_text_obj.bbox.y1 - self.bbox_padding,
            end_text_obj.bbox.x2 + self.bbox_padding,
            end_text_obj.bbox.y2 + self.bbox_padding
        )

        # 添加标注
        annotations.append(self.transform_annotation(label, start_bbox.xyxy))
        annotations.append(self.transform_annotation(label, end_bbox.xyxy))

        return annotations

    @staticmethod
    def _generate_normal_points_by_line(point1, point2):
        # 计算线段上的标记点
        if random.random() < 0.5:  # 50%概率在1/2处添加一个标记
            marker_points = [(point1.x + point2.x) / 2, (point1.y + point2.y) / 2]
        else:  # 50%概率在1/3和2/3处添加两个标记
            marker_points = [
                (2 * point1.x + point2.x) / 3, (2 * point1.y + point2.y) / 3,
                (point1.x + 2 * point2.x) / 3, (point1.y + 2 * point2.y) / 3
            ]

        points = []
        for k in range(0, len(marker_points), 2):
            marker_x, marker_y = marker_points[k], marker_points[k + 1]
            points.append(Point(marker_x, marker_y))
        return points

    def draw_normal_point(self, point, text) -> List[str]:
        annotations = []
        marker_x, marker_y = point.xy

        # 绘制圆形标记
        normal_point_radius = 5
        self.draw_circle(point, random.randint(10, 12), CircleFillPattern.NONE)

        # 生成并绘制编号
        label_num = f'{text}'
        # 在圆形右侧添加编号
        label_x = marker_x + (normal_point_radius + 10) * random_pos_neg()
        label_y = marker_y + (normal_point_radius + 10) * random_pos_neg()

        # 绘制文本并获取其边界框
        text_obj = self.draw_text(label_num, Point(label_x, label_y), 0, font_scale=0.7)
        text_bbox = text_obj.bbox

        # 确定圆形的边界框
        circle_bbox = BoundingBox(
            marker_x - normal_point_radius,
            marker_y - normal_point_radius,
            marker_x + normal_point_radius,
            marker_y + normal_point_radius
        )

        # 合并两个边界框
        merged_bbox = BoundingBox(
            min(circle_bbox.x1, text_bbox.x1) - self.bbox_padding - 5,
            min(circle_bbox.y1, text_bbox.y1) - self.bbox_padding - 5,
            max(circle_bbox.x2, text_bbox.x2) + self.bbox_padding + 5,
            max(circle_bbox.y2, text_bbox.y2) + self.bbox_padding + 5
        )

        # 返回合并后的边界框的标注
        annotations.append(self.transform_annotation(YoloLabelType.NORM_DH, merged_bbox.xyxy))
        return annotations

    def generate_corners_annotations(self, corners: List[Point]) -> List[str]:
        # 生成引脚标注
        annotations = []
        # 为每个角点生成引脚标注
        for i, point in enumerate(corners):
            # 根据角点位置确定引脚方向
            if i == 0:  # 左上角
                # 斜线指向左上方 (135度方向)
                angle = math.pi * 3 / 4  # 135度
                # 水平线在斜线左边
                horizontal_direction = -1
            elif i == 1:  # 右上角
                # 斜线指向右上方 (45度方向)
                angle = math.pi / 4  # 45度
                # 水平线在斜线右边
                horizontal_direction = 1
            elif i == 2:  # 右下角
                # 斜线指向右下方 (315度/-45度方向)
                angle = -math.pi / 4  # -45度
                # 水平线在斜线右边
                horizontal_direction = 1
            else:  # 左下角 (i == 3)
                # 斜线指向左下方 (225度方向)
                angle = -math.pi * 3 / 4  # -135度
                # 水平线在斜线左边
                horizontal_direction = -1

            # 计算斜线的长度（100-200像素）
            line_length = random.randint(50, 80)

            # 计算斜线的终点
            end_x = point.x + line_length * math.cos(angle)
            end_y = point.y - line_length * math.sin(angle)

            # 计算水平线的长度（150-250像素）
            horizontal_length = random.randint(100, 120)

            # 根据预定义的方向绘制水平线
            horizontal_end_x = end_x + horizontal_direction * horizontal_length

            # 绘制斜线
            self.draw_line(point, Point(end_x, end_y), line_type=LineType.LINE)

            # 绘制水平线
            self.draw_line(
                Point(end_x, end_y),
                Point(horizontal_end_x, end_y),
                line_type=LineType.LINE
            )

            # 生成描述文本
            upper_text, down_text = RandomGroupGenerator.generate_drawing_text_pair('var_assignment')

            # 计算文本位置（水平线中点上方和下方）
            text_x = (end_x + horizontal_end_x) / 2
            text_y = end_y

            horizontal_spacing = 15

            upper_text_obj = self.draw_text(
                upper_text,
                Point(text_x, text_y - horizontal_spacing),
                angle=0,
                font_scale=0.7
            )

            # 绘制下方文本并获取其边界框
            down_text_obj = self.draw_text(
                down_text,
                Point(text_x, text_y + horizontal_spacing),
                angle=0,
                font_scale=0.7
            )

            # 合并两个文本的边界框
            bbox = BoundingBox(
                min(upper_text_obj.bbox.x1, down_text_obj.bbox.x1, end_x) - self.bbox_padding,
                min(upper_text_obj.bbox.y1, down_text_obj.bbox.y1) - self.bbox_padding,
                max(upper_text_obj.bbox.x2, down_text_obj.bbox.x2) + self.bbox_padding,
                max(upper_text_obj.bbox.y2, down_text_obj.bbox.y2) + self.bbox_padding
            )
            annotations.append(self.transform_annotation(YoloLabelType.PIN_ANNO, bbox.xyxy))
        return annotations

    def _generate_building(self, center: Point, max_width: int = 1000, max_height: int = 800, draw: bool = True) -> \
    List[Point]:
        """Generate a random building at the specified center point.

        Args:
            center: The approximate center point where the building will be generated
            max_width: Maximum width of the building
            max_height: Maximum height of the building
            draw: Whether to draw the building on the image

        Returns:
            List[Point]: List of points representing the building contour
        """
        # 随机决定建筑物的组成部分数量（2-4个矩形）
        num_parts = random.randint(2, 4)

        # 随机决定建筑物的整体大小
        building_width = random.randint(max_width // 2, max_width)
        building_height = random.randint(max_height // 2, max_height)

        # 计算建筑物的边界
        min_x = max(50, int(center.x - building_width // 2))
        min_y = max(50, int(center.y - building_height // 2))
        max_x = min(self.image_size[0] - 50, int(center.x + building_width // 2))
        max_y = min(self.image_size[1] - 50, int(center.y + building_height // 2))

        # 生成每个部分
        parts = []
        attempts = 0
        max_attempts = 50  # 防止无限循环

        while len(parts) < num_parts and attempts < max_attempts:
            attempts += 1

            # 随机决定这个部分的大小
            part_width = random.randint(building_width // 4, building_width // 2)
            part_height = random.randint(building_height // 4, building_height // 2)

            # 随机决定这个部分的位置
            part_x = random.randint(min_x, max(min_x, max_x - part_width))
            part_y = random.randint(min_y, max(min_y, max_y - part_height))

            # 检查与现有部分的重叠率
            overlap_ratio = 0
            for existing_x, existing_y, existing_width, existing_height in parts:
                # 计算重叠区域
                overlap_x1 = max(part_x, existing_x)
                overlap_y1 = max(part_y, existing_y)
                overlap_x2 = min(part_x + part_width, existing_x + existing_width)
                overlap_y2 = min(part_y + part_height, existing_y + existing_height)

                if overlap_x2 > overlap_x1 and overlap_y2 > overlap_y1:
                    overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
                    part_area = part_width * part_height
                    overlap_ratio = max(overlap_ratio, overlap_area / part_area)

            # 如果重叠率小于40%，添加这个部分
            if overlap_ratio < 0.4:
                parts.append((part_x, part_y, part_width, part_height))

        if not parts:
            return []

        # 创建一个临时图像用于绘制建筑物，使用更高的分辨率以获得更好的轮廓检测
        temp_scale = 2  # 临时放大倍数
        temp_size = (self.image_size[0] * temp_scale, self.image_size[1] * temp_scale)
        building_img = Image.new('L', temp_size, 0)  # 使用灰度图像
        building_draw = ImageDraw.Draw(building_img)

        # 绘制所有部分到临时图像（放大坐标）
        for x, y, width, height in parts:
            building_draw.rectangle(
                [x * temp_scale, y * temp_scale,
                 (x + width) * temp_scale, (y + height) * temp_scale],
                fill=255
            )

        # 转换为numpy数组进行轮廓检测
        building_array = np.array(building_img)

        # 使用形态学操作来清理图像
        kernel = np.ones((3, 3), np.uint8)
        building_array = cv2.morphologyEx(building_array, cv2.MORPH_CLOSE, kernel)

        # 查找外轮廓
        contours, _ = cv2.findContours(building_array, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 获取轮廓点
        contour_points = []
        for contour in contours:
            # 将轮廓点缩放回原始尺寸
            points = [(point[0][0] // temp_scale, point[0][1] // temp_scale) for point in contour]

            # 简化轮廓以减少点数
            if len(points) > 3:
                # 使用道格拉斯-普克算法简化轮廓
                epsilon = 2.0  # 简化程度，值越大简化越多
                contour_scaled = np.array([[p] for p in points], dtype=np.int32)
                simplified = cv2.approxPolyDP(contour_scaled, epsilon, True)
                points = [(point[0][0], point[0][1]) for point in simplified]

            # 确保有足够的点来绘制多边形
            if len(points) > 2:
                contour_points.extend([Point(x, y) for x, y in points])

                # 如果需要绘制，则绘制到主图像上
                if draw:
                    self.draw.polygon(points, outline=(0, 0, 0), width=2, fill=(255, 255, 255))

                    # 添加细节（窗户或门）
                    for part_idx, (x, y, width, height) in enumerate(parts):
                        if random.random() < 0.7:  # 70%的概率添加细节
                            num_details = random.randint(1, 3)
                            for _ in range(num_details):
                                detail_width = random.randint(width // 8, width // 4)
                                detail_height = random.randint(height // 8, height // 4)

                                # 确保细节在矩形范围内
                                max_detail_x = max(width // 8, width - detail_width - width // 8)
                                max_detail_y = max(height // 8, height - detail_height - height // 8)

                                if max_detail_x > width // 8 and max_detail_y > height // 8:
                                    detail_x = x + random.randint(width // 8, max_detail_x)
                                    detail_y = y + random.randint(height // 8, max_detail_y)

                                    # 检查细节是否在建筑物的实际区域内
                                    detail_center_x = detail_x + detail_width // 2
                                    detail_center_y = detail_y + detail_height // 2

                                    # 简单检查：确保细节中心在当前部分内
                                    if (x <= detail_center_x <= x + width and
                                            y <= detail_center_y <= y + height):
                                        # 绘制细节（小矩形）
                                        self.draw.rectangle(
                                            [detail_x, detail_y, detail_x + detail_width, detail_y + detail_height],
                                            outline=(0, 0, 0),
                                            width=1
                                        )

        return contour_points

    def generate_noise(self, core_contour: List[Point]) -> None:
        """Generate noise elements outside the main layout."""
        # 生成杂草或小圆形
        self._generate_staggered_pattern(padding=10)

        # 生成建筑物（确保不与核心区域重叠）
        building_contours = []
        num_buildings = random.randint(4, 6)
        max_attempts = 100  # 最大尝试次数

        for _ in range(num_buildings):
            attempts = 0
            while attempts < max_attempts:
                # 随机选择建筑物位置
                center_x = random.randint(200, self.image_size[0] - 200)
                center_y = random.randint(200, self.image_size[1] - 200)

                # 生成建筑物（不立即绘制）
                building_contour = self._generate_building(Point(center_x, center_y), 1000, 800, draw=False)

                if not building_contour:
                    attempts += 1
                    continue

                # 检查是否与核心区域重叠
                if self.check_polygon_overlap(building_contour, core_contour):
                    attempts += 1
                    continue

                # 检查是否与其他建筑物重叠
                overlap = False
                for existing_contour in building_contours:
                    if self.check_polygon_overlap(building_contour, existing_contour):
                        overlap = True
                        break

                if not overlap:
                    building_contours.append(building_contour)
                    # 找到合适的位置后，再绘制建筑物
                    self._generate_building(Point(center_x, center_y), 1000, 800, draw=True)
                    break

                attempts += 1

        # 生成马路（确保不与核心区域和建筑物重叠）
        max_road_attempts = 100  # 最大尝试次数

        for _ in range(max_road_attempts):
            # 生成马路（不立即绘制）
            road_contour = self._generate_road(draw=False)

            # 检查是否与核心区域重叠
            if self.check_polygon_overlap(road_contour, core_contour):
                continue

            # 检查是否与建筑物重叠
            overlap = False
            for building_contour in building_contours:
                if self.check_polygon_overlap(road_contour, building_contour):
                    overlap = True
                    break

            if not overlap:
                # 找到合适的位置后，再绘制马路
                self._generate_road(draw=True)
                break

        num_noise = random.randint(35, 50)
        for _ in range(num_noise):
            noise_type = random.choice(["line", "text", "circle"])

            if noise_type == "line":
                start = Point(
                    random.randint(0, self.image_size[0]),
                    random.randint(0, self.image_size[1])
                )
                end = Point(
                    random.randint(0, self.image_size[0]),
                    random.randint(0, self.image_size[1])
                )
                self.draw_line(start, end, line_type=LineType.LINE)

            elif noise_type == "text":
                text = f"T{random.randint(1, 100)}"
                position = Point(
                    random.randint(0, self.image_size[0]),
                    random.randint(0, self.image_size[1])
                )
                angle = random.uniform(0, 2 * np.pi)
                self.draw_text(text, position, angle)

            elif noise_type == "circle":
                center = Point(
                    random.randint(0, self.image_size[0]),
                    random.randint(0, self.image_size[1])
                )
                radius = random.randint(5, 20)
                self.draw_circle(center, radius, CircleFillPattern.NONE)

    def generate_table(
            self,
            rows: int = 5, cols: int = 8,
            table_width: int = 600, table_height: int = 250,
            margin: int = 50, font_size: int = 26,
            merged_cells: List[Tuple[int, int, int, int]] = None,
    ) -> None:
        """Generate a table in the bottom right corner of the drawing.

        Args:
            rows: Number of rows in the table
            cols: Number of columns in the table
            table_width: Width of the table
            table_height: Height of the table
            margin: Margin from the bottom right corner
            font_size: Font size for the text in the table
            merged_cells: List of tuples (start_row, start_col, end_row, end_col) representing merged cells
        """
        # Table dimensions and position
        cell_width = table_width // cols
        cell_height = table_height // rows

        # Position the table in the bottom right corner with some margin
        start_x = self.image_size[0] - table_width - margin
        start_y = self.image_size[1] - table_height - margin

        # Draw table borders
        self.draw.rectangle(
            [start_x, start_y, start_x + table_width, start_y + table_height],
            fill=(255, 255, 255),
            outline=(0, 0, 0),
            width=2
        )

        # Draw horizontal lines
        for i in range(1, rows):
            y = start_y + i * cell_height
            self.draw.line(
                [(start_x, y), (start_x + table_width, y)],
                fill=(0, 0, 0),
                width=1
            )

        # Draw vertical lines
        for i in range(1, cols):
            x = start_x + i * cell_width
            self.draw.line(
                [(x, start_y), (x, start_y + table_height)],
                fill=(0, 0, 0),
                width=1
            )

        # Process merged cells
        merged_regions = []
        if merged_cells:
            for start_row, start_col, end_row, end_col in merged_cells:
                # Calculate merged cell dimensions
                merged_width = (end_col - start_col + 1) * cell_width
                merged_height = (end_row - start_row + 1) * cell_height

                # Calculate merged cell position
                merged_x = start_x + start_col * cell_width
                merged_y = start_y + start_row * cell_height

                # Store merged region
                merged_regions.append({
                    'x': merged_x,
                    'y': merged_y,
                    'width': merged_width,
                    'height': merged_height,
                    'rows': end_row - start_row + 1,
                    'cols': end_col - start_col + 1
                })

                # Draw merged cell border
                self.draw.rectangle(
                    [merged_x, merged_y, merged_x + merged_width, merged_y + merged_height],
                    fill=(255, 255, 255),
                    outline=(0, 0, 0),
                    width=2
                )

        # Fill cells with content
        for row in range(rows):
            for col in range(cols):
                # Check if this cell is part of a merged region
                is_merged = False
                for region in merged_regions:
                    if (
                            (region['y'] - start_y) // cell_height <= row <= (region['y'] - start_y) // cell_height +
                            region['rows'] - 1 and
                            (region['x'] - start_x) // cell_width <= col <= (region['x'] - start_x) // cell_width +
                            region['cols'] - 1):
                        is_merged = True
                        break

                if not is_merged:
                    # Calculate cell position
                    cell_x = start_x + col * cell_width
                    cell_y = start_y + row * cell_height

                    # Generate random content (50% chance for number, 50% chance for text)
                    if random.random() < 0.5:
                        content = str(random.randint(0, 999))
                    else:
                        content = ''.join(random.choices(string.ascii_uppercase, k=random.randint(2, 4)))

                    # Calculate text position (centered in cell)
                    font = self.get_font(font_size)

                    # Get text size
                    bbox = self.draw.textbbox((0, 0), content, font=font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]

                    # Calculate text position to center it in the cell
                    text_x = cell_x + (cell_width - text_width) // 2
                    text_y = cell_y + (cell_height - text_height) // 2

                    # Draw text
                    self.draw.text((text_x, text_y), content, fill=(0, 0, 0), font=font)

        # Fill merged cells with content
        for region in merged_regions:
            # Generate content for merged cell
            r = random.random()
            if r < 0.2:
                content = str(random.randint(1000, 9999))
            elif r < 0.5:
                content = ''.join(random.choices(string.ascii_uppercase, k=random.randint(4, 8)))
            else:
                content = RandomTextGenerator.generate_random_chinese_text(random.randint(3, 5))

            # Calculate text position (centered in merged cell)
            font = self.get_font(font_size)

            # Get text size
            bbox = self.draw.textbbox((0, 0), content, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            # Calculate text position to center it in the merged cell
            text_x = region['x'] + (region['width'] - text_width) // 2
            text_y = region['y'] + (region['height'] - text_height) // 2

            # Draw text
            self.draw.text((text_x, text_y), content, fill=(0, 0, 0), font=font)

    def generate_weed(self, center: Point) -> None:
        """Generate a weed pattern at the specified center point.

        Args:
            center: The center point where the weed will be generated
        """
        # Length of the main horizontal line
        main_length = random.randint(8, 12)
        # Length of the diagonal lines
        diagonal_length = random.randint(6, 10)

        # Calculate the main horizontal line endpoints
        start_x = center.x - main_length // 2
        end_x = center.x + main_length // 2
        y = center.y

        # Draw the main horizontal line (0 degrees)
        self.draw.line([(start_x, y), (end_x, y)], fill=(0, 0, 0), width=1)

        # Draw the diagonal lines
        angles = [45, 90, 135]  # Angles in degrees
        for angle in angles:
            # Convert angle to radians
            angle_rad = math.radians(angle)
            # Calculate end point of diagonal line
            end_x = center.x + diagonal_length * math.cos(angle_rad)
            end_y = center.y - diagonal_length * math.sin(angle_rad)
            # Draw the diagonal line
            self.draw.line([(center.x, center.y), (end_x, end_y)], fill=(0, 0, 0), width=1)

    def generate_small_circle(self, center: Point) -> None:
        """Generate a small circle at the specified center point.

        Args:
            center: The center point where the circle will be generated
        """
        # Random radius between 2 and 4 pixels
        radius = random.randint(2, 4)
        # Draw the circle
        self.draw.ellipse([
            center.x - radius,
            center.y - radius,
            center.x + radius,
            center.y + radius
        ], outline=(0, 0, 0), width=1)

    def _generate_road(self, draw: bool = True) -> List[Point]:
        """Generate a road with name and weeds.

        The road consists of:
        - Two pairs of parallel lines (road edges)
        - Road name in the middle
        - Weeds along the road
        - A gap in the middle of each side

        Args:
            draw: Whether to draw the road on the image

        Returns:
            List[Point]: List of points representing the road contour
        """
        # 随机生成路的宽度和间距
        road_width = random.randint(120, 200)
        line_gap = 15  # 路边缘两条线的间距
        center_gap = 40  # 边缘中间区域的间距

        # 随机生成角度（0到360度）
        angle = random.uniform(0, 2 * np.pi)

        # 随机选择中心点（避开边缘区域）
        center_x = random.randint(200, self.image_size[0] - 200)
        center_y = random.randint(200, self.image_size[1] - 200)

        # 计算垂直于路方向的单位向量
        perp_x = -math.sin(angle)
        perp_y = math.cos(angle)

        # 计算路边缘的偏移量
        edge_offset = road_width / 2
        line_offset = line_gap / 2

        # 计算四个边缘线的起点和终点
        # 使用参数方程计算线与图纸边界的交点
        def find_intersection(start_x, start_y, dx, dy):
            t = 0
            while True:
                t += 1
                test_x = start_x + t * dx
                test_y = start_y + t * dy
                if (test_x < 0 or test_x > self.image_size[0] or
                        test_y < 0 or test_y > self.image_size[1]):
                    return start_x + (t - 1) * dx, start_y + (t - 1) * dy

        # 计算四个边缘线的起点和终点
        # 上路边缘
        up_start_x = center_x + (edge_offset + line_offset) * perp_x
        up_start_y = center_y + (edge_offset + line_offset) * perp_y
        up_end_x, up_end_y = find_intersection(up_start_x, up_start_y, math.cos(angle), math.sin(angle))
        up_start_x2, up_start_y2 = find_intersection(up_start_x, up_start_y, -math.cos(angle), -math.sin(angle))

        # 上路内线
        up_inner_start_x = center_x + (edge_offset - line_offset) * perp_x
        up_inner_start_y = center_y + (edge_offset - line_offset) * perp_y
        up_inner_end_x, up_inner_end_y = find_intersection(up_inner_start_x, up_inner_start_y, math.cos(angle),
                                                           math.sin(angle))
        up_inner_start_x2, up_inner_start_y2 = find_intersection(up_inner_start_x, up_inner_start_y, -math.cos(angle),
                                                                 -math.sin(angle))

        # 下路边缘
        down_start_x = center_x - (edge_offset + line_offset) * perp_x
        down_start_y = center_y - (edge_offset + line_offset) * perp_y
        down_end_x, down_end_y = find_intersection(down_start_x, down_start_y, math.cos(angle), math.sin(angle))
        down_start_x2, down_start_y2 = find_intersection(down_start_x, down_start_y, -math.cos(angle), -math.sin(angle))

        # 下路内线
        down_inner_start_x = center_x - (edge_offset - line_offset) * perp_x
        down_inner_start_y = center_y - (edge_offset - line_offset) * perp_y
        down_inner_end_x, down_inner_end_y = find_intersection(down_inner_start_x, down_inner_start_y, math.cos(angle),
                                                               math.sin(angle))
        down_inner_start_x2, down_inner_start_y2 = find_intersection(down_inner_start_x, down_inner_start_y,
                                                                     -math.cos(angle), -math.sin(angle))

        # 如果需要绘制，则绘制路边缘（四条线）
        if draw:
            road_color = (128, 128, 128)  # 灰色

            # 绘制上路边缘
            self.draw.line([(up_start_x2, up_start_y2), (up_end_x, up_end_y)], fill=road_color, width=1)
            self.draw.line([(up_inner_start_x2, up_inner_start_y2), (up_inner_end_x, up_inner_end_y)], fill=road_color,
                           width=1)

            # 绘制下路边缘
            self.draw.line([(down_start_x2, down_start_y2), (down_end_x, down_end_y)], fill=road_color, width=1)
            self.draw.line([(down_inner_start_x2, down_inner_start_y2), (down_inner_end_x, down_inner_end_y)],
                           fill=road_color, width=1)

            # 在中心点附近添加路名
            road_name = RandomTextGenerator.generate_random_chinese_text(random.randint(5, 8))
            font_size = 20
            font = self.get_font(font_size)

            # 计算路名位置（在中心点附近）
            name_offset = 0  # 路名到中心点的距离
            name_x = center_x + name_offset * perp_x
            name_y = center_y + name_offset * perp_y

            # 绘制路名
            self.draw_text(road_name, Point(name_x, name_y), math.degrees(angle), spacing=40)

            # 在路两侧添加杂草
            num_weeds = random.randint(10, 20)  # 每侧的杂草数量
            for side in [-1, 1]:  # -1表示上路边缘，1表示下路边缘
                # 计算这一侧的中心线
                side_center_x = center_x + side * (edge_offset + center_gap) * perp_x
                side_center_y = center_y + side * (edge_offset + center_gap) * perp_y

                # 在中心线两侧添加杂草
                for _ in range(num_weeds):
                    # 随机选择位置（在中心点两侧）
                    t = random.uniform(-0.4, 0.4)  # 在中心点附近添加杂草
                    weed_x = center_x + t * (up_end_x - up_start_x2)
                    weed_y = center_y + t * (up_end_y - up_start_y2)

                    # 添加随机偏移
                    offset = random.uniform(-10, 10)
                    weed_x += side * (edge_offset + center_gap) * perp_x + offset * math.cos(angle)
                    weed_y += side * (edge_offset + center_gap) * perp_y + offset * math.sin(angle)

                    # 确保杂草在图纸范围内
                    if (0 <= weed_x <= self.image_size[0] and
                            0 <= weed_y <= self.image_size[1]):
                        # 生成杂草
                        self.generate_weed(Point(weed_x, weed_y))

        # 返回路的轮廓点
        road_contour = [
            Point(up_start_x2, up_start_y2),
            Point(up_end_x, up_end_y),
            Point(down_end_x, down_end_y),
            Point(down_start_x2, down_start_y2)
        ]

        return road_contour

    def _generate_staggered_pattern(
            self, grid_width: int = 100, grid_height: int = 80, padding: int = 100
    ) -> None:
        """Generate weeds and small circles in a staggered pattern across the image."""
        # 计算可以放置的行数和列数
        num_cols = (self.image_size[0] - padding) // grid_width  # 留出左右边距
        num_rows = (self.image_size[1] - padding) // grid_height  # 留出上下边距

        # 为每一行添加随机偏移
        row_offsets = [random.randint(-20, 20) for _ in range(num_rows)]

        # 生成错位图案
        for row in range(num_rows):
            # 计算当前行的y坐标（加上随机偏移）
            y = 50 + row * grid_height + row_offsets[row]

            # 计算当前行的列数（偶数行和奇数行错开）
            current_cols = num_cols if row % 2 == 0 else num_cols - 1

            for col in range(current_cols):
                # 计算当前列的x坐标（偶数行和奇数行错开）
                x = 50 + col * grid_width
                if row % 2 == 1:  # 奇数行向右偏移半个网格
                    x += grid_width // 2

                # 添加小的随机偏移
                x += random.randint(-10, 10)
                y += random.randint(-10, 10)

                # 确保坐标在图像范围内
                x = max(50, min(x, self.image_size[0] - 50))
                y = max(50, min(y, self.image_size[1] - 50))

                # 50%概率生成杂草或小圆形
                if random.random() < 0.5:
                    self.generate_weed(Point(x, y))
                else:
                    self.generate_small_circle(Point(x, y))

    def draw_drill_hole_label(self, position: Point, letter_num: str, upper_text: str, down_text: str,
                              font_scale: float = 0.7) -> List[str]:
        """Draw a drill hole label with three parts: letter number, upper text and down text.

        Args:
            position: The position where the label will be drawn
            letter_num: The letter number to be drawn on the left
            upper_text: The text to be drawn above the line
            down_text: The text to be drawn below the line
            font_scale: Scale factor for the font size
        """
        # 创建字体
        font_size = int(32 * font_scale)
        font = self.get_font(font_size, True)

        # 计算各个文本的尺寸
        letter_bbox = font.getbbox(letter_num)
        upper_bbox = font.getbbox(upper_text)
        down_bbox = font.getbbox(down_text)

        # 计算各个文本的宽度和高度
        letter_width = letter_bbox[2] - letter_bbox[0]
        upper_width = upper_bbox[2] - upper_bbox[0]
        down_width = down_bbox[2] - down_bbox[0]
        text_height = max(
            letter_bbox[3] - letter_bbox[1],
            upper_bbox[3] - upper_bbox[1],
            down_bbox[3] - down_bbox[1],
        )

        # 计算分隔线的长度（取上下文本的最大宽度）
        line_length = max(upper_width, down_width)

        # 计算整体布局
        vertical_spacing = 5  # 垂直间距
        horizontal_spacing = 30  # 水平间距

        # 计算各个元素的位置

        # 字母编号位置（在分隔线左侧）
        letter_x = position.x
        letter_y = position.y + text_height / 2

        # 微调位置
        offset_x = 45
        offset_y = 10
        line_offset_y = text_height / 2

        # 分隔线位置
        line_x1 = letter_x + letter_width + horizontal_spacing - offset_x
        line_y = letter_y - line_offset_y + offset_y
        line_x2 = line_x1 + line_length

        # 上文本位置
        upper_x = line_x1 + offset_x
        upper_y = line_y - text_height - vertical_spacing + offset_y

        # 下文本位置
        down_x = line_x1 + offset_x
        down_y = line_y + vertical_spacing + offset_y

        # 绘制字母编号
        self.draw_text(letter_num, Point(letter_x, letter_y), 0, font_scale=font_scale)

        # 绘制分隔线
        self.draw.line(
            [(line_x1, line_y), (line_x2, line_y)],
            fill=(0, 0, 0),
            width=1
        )

        # 绘制上文本
        self.draw_text(upper_text, Point(upper_x, upper_y), 0, font_scale=font_scale)

        # 绘制下文本
        self.draw_text(down_text, Point(down_x, down_y), 0, font_scale=font_scale)

        # 确定 bbox，考虑所有文本和分隔线
        bbox = BoundingBox(
            min(letter_x, line_x1, upper_x, down_x) - self.bbox_padding - 10,
            min(upper_y, down_y) - self.bbox_padding - 10,
            max(line_x2, letter_x + letter_width + horizontal_spacing) + self.bbox_padding + 10,
            max(upper_y + text_height + vertical_spacing,
                down_y + text_height + vertical_spacing) + self.bbox_padding +5
        )
        # 返回注释信息
        return [self.transform_annotation(YoloLabelType.DH_DEP, bbox.xyxy)]

    def generate_json_annotations(self, img_id, dotted_lines, core_drill_holes):
        """生成JSON格式的标注文件"""
        json_data = {
            "version": "3.1.1",
            "flags": {},
            "shapes": [],
            "imagePath": f"{img_id:06d}.png",
            "imageData": None,
            "imageHeight": self.image_size[1],
            "imageWidth": self.image_size[0]
        }
        
        # 添加虚线段标注
        for line in dotted_lines:
            start, end = line
            shape = {
                "label": "line",
                "score": None,
                "points": [
                    [start.x, start.y],
                    [end.x, end.y]
                ],
                "group_id": None,
                "description": "",
                "difficult": False,
                "shape_type": "line",
                "flags": {},
                "attributes": {},
                "kie_linking": []
            }
            json_data["shapes"].append(shape)
            
        # 添加钻孔标注
        for drill in core_drill_holes:
            # 获取钻孔边界框坐标
            x1, y1, x2, y2 = drill["bbox"]
            shape = {
                "kie_linking": [],
                "label": "drill",
                "score": None,
                "points": [
                    [x1, y1],  # 左上角
                    [x2, y1],  # 右上角
                    [x2, y2],  # 右下角
                    [x1, y2]   # 左下角
                ],
                "group_id": None,
                "description": "",
                "difficult": False,
                "shape_type": "rectangle",
                "flags": {},
                "attributes": {}
            }
            json_data["shapes"].append(shape)
            
        return json_data


def main():
    # 创建数据集生成器
    output_dir = Path(r'D:\PCITC\SH_project_data\star_plan_dataset\synthetic_plan_dataset')
    # output_dir = DATASETS
    output_dir.mkdir(parents=True, exist_ok=True)
    generator = DrawingDatasetGenerator(image_size=(1960, 1080), output_dir=output_dir)

    # 生成数据集 (可以调整数量)
    generator.generate_dataset(num_train=400, num_val=0)


if __name__ == "__main__":
    main()
