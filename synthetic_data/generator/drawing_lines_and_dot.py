import os
import cv2
import numpy as np
import random
import json
from pathlib import Path
from typing import List, Tuple, Dict, Any


class YoloLinePointDatasetGenerator:
    def __init__(self,
                 output_dir: str = "generated_dataset",
                 image_size: int = 1024,
                 min_lines: int = 5,
                 max_lines: int = 8,
                 min_dashed: int = 5,
                 max_dashed: int = 8,
                 min_drills: int = 3,
                 max_drills: int = 6,
                 min_points: int = 15,
                 max_points: int = 20,
                 point_radius: int = 4,
                 line_width: int = 2,
                 drill_radius_range: Tuple[int, int] = (15, 30),
                 point_on_line_ratio: float = 0.5):
        """
        初始化数据集生成器

        参数:
            output_dir: 输出目录路径
            image_size: 生成图像的尺寸(正方形)
            min_lines: 最少实线数量
            max_lines: 最多实线数量
            min_dashed: 最少虚线数量
            max_dashed: 最多虚线数量
            min_drills: 最少钻孔数量
            max_drills: 最多钻孔数量
            min_points: 最少红点数量
            max_points: 最多红点数量
            point_radius: 红点半径(像素)
            line_width: 线宽(像素)
            drill_radius_range: 钻孔半径范围(像素)
            point_on_line_ratio: 红点在直线上的比例(0-1)
        """
        self.output_dir = output_dir
        self.image_size = image_size
        self.min_lines = min_lines
        self.max_lines = max_lines
        self.min_dashed = min_dashed
        self.max_dashed = max_dashed
        self.min_drills = min_drills
        self.max_drills = max_drills
        self.min_points = min_points
        self.max_points = max_points
        self.point_radius = point_radius
        self.line_width = line_width
        self.drill_radius_range = drill_radius_range
        self.point_on_line_ratio = point_on_line_ratio

        # 创建输出目录 - 所有文件都放在同一目录下
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)

    def _generate_json_annotation(self, img_id: int, dashed_lines: List[Tuple[Tuple[int, int], Tuple[int, int]]],
                                 drills: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成JSON格式的标注文件"""
        json_data = {
            "version": "3.1.1",
            "flags": {},
            "shapes": []
        }

        # 添加虚线段标注
        for line in dashed_lines:
            start, end = line
            shape = {
                "label": "line",
                "score": None,
                "points": [
                    [start[0], start[1]],
                    [end[0], end[1]]
                ],
                "group_id": None,
                "description": "",
                "difficult": False,
                "shape_type": "line",
                "flags": {},
                "attributes": {},
                "kie_linking": []
            }
            json_data["shapes"].append(shape)

        # 添加钻孔标注
        for drill in drills:
            center_x, center_y = drill["center"]
            radius = drill["radius"]
            shape = {
                "kie_linking": [],
                "label": "drill",
                "score": None,
                "points": [
                    [center_x - radius, center_y - radius],  # 左上角
                    [center_x + radius, center_y - radius],  # 右上角
                    [center_x + radius, center_y + radius],  # 右下角
                    [center_x - radius, center_y + radius]   # 左下角
                ],
                "group_id": None,
                "description": "",
                "difficult": False,
                "shape_type": "rectangle",
                "flags": {},
                "attributes": {}
            }
            json_data["shapes"].append(shape)

        return json_data

    def _generate_line_points(self, num_points: int, line: Tuple[Tuple[int, int], Tuple[int, int]]) -> List[
        Tuple[int, int]]:
        """在直线上生成均匀分布的点"""
        (x1, y1), (x2, y2) = line
        points = []
        for i in range(num_points):
            t = i / (num_points - 1) if num_points > 1 else 0.5
            x = int(x1 + t * (x2 - x1))
            y = int(y1 + t * (y2 - y1))
            points.append((x, y))
        return points

    def _point_on_line(self,
                       point: Tuple[int, int],
                       line: Tuple[Tuple[int, int], Tuple[int, int]],
                       threshold: int = 5) -> bool:
        """检查点是否在直线上(包括虚线)"""
        (x, y), ((x1, y1), (x2, y2)) = point, line

        # 检查点是否在线段范围内
        if min(x1, x2) - threshold <= x <= max(x1, x2) + threshold and \
                min(y1, y2) - threshold <= y <= max(y1, y2) + threshold:
            # 计算点到直线的距离
            if x1 == x2:  # 垂直线
                return abs(x - x1) <= threshold
            elif y1 == y2:  # 水平线
                return abs(y - y1) <= threshold
            else:
                distance = abs((y2 - y1) * x - (x2 - x1) * y + x2 * y1 - y2 * x1) / np.sqrt(
                    (y2 - y1) ** 2 + (x2 - x1) ** 2)
                return distance <= threshold
        return False

    def _draw_dashed_line(self,
                          img: np.ndarray,
                          start: Tuple[int, int],
                          end: Tuple[int, int],
                          color: Tuple[int, int, int],
                          thickness: int = 2):
        """绘制虚线"""
        x1, y1 = start
        x2, y2 = end
        length = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        dash_length = max(10, int(length / 10))

        dx, dy = (x2 - x1) / length, (y2 - y1) / length
        for i in range(0, int(length), dash_length * 2):
            segment_start = (int(x1 + dx * i), int(y1 + dy * i))
            segment_end = (int(x1 + dx * (i + dash_length)), int(y1 + dy * (i + dash_length)))
            cv2.line(img, segment_start, segment_end, color, thickness)

    def generate_image(self, img_idx: int) -> np.ndarray:
        """生成单张图像和对应的标注文件"""
        # 创建空白图像
        img = np.ones((self.image_size, self.image_size, 3), dtype=np.uint8) * 255

        # 存储所有直线、虚线和钻孔
        lines = []
        dashed_lines = []
        drills = []

        # 1. 绘制实线 (黑色)
        num_lines = random.randint(self.min_lines, self.max_lines)
        for _ in range(num_lines):
            x1, y1 = random.randint(50, self.image_size-50), random.randint(50, self.image_size-50)
            x2, y2 = random.randint(50, self.image_size-50), random.randint(50, self.image_size-50)
            cv2.line(img, (x1, y1), (x2, y2), (0, 0, 0), self.line_width)
            lines.append(((x1, y1), (x2, y2)))

        # 2. 绘制虚线 (黑色) - 这些将被标注
        num_dashed = random.randint(self.min_dashed, self.max_dashed)
        for _ in range(num_dashed):
            x1, y1 = random.randint(50, self.image_size-50), random.randint(50, self.image_size-50)
            x2, y2 = random.randint(50, self.image_size-50), random.randint(50, self.image_size-50)
            self._draw_dashed_line(img, (x1, y1), (x2, y2), (0, 0, 0), self.line_width)
            dashed_lines.append(((x1, y1), (x2, y2)))

        # 3. 绘制钻孔 (圆形) - 这些将被标注
        num_drills = random.randint(self.min_drills, self.max_drills)
        for _ in range(num_drills):
            # 确保钻孔不会超出图像边界
            radius = random.randint(*self.drill_radius_range)
            x = random.randint(radius + 10, self.image_size - radius - 10)
            y = random.randint(radius + 10, self.image_size - radius - 10)

            # 绘制钻孔 (空心圆)
            cv2.circle(img, (x, y), radius, (0, 0, 0), self.line_width)

            # 存储钻孔信息
            drills.append({
                "center": (x, y),
                "radius": radius
            })

        # 4. 生成一些装饰性的红点 (不进行标注)
        num_points = random.randint(self.min_points, self.max_points)
        for _ in range(num_points):
            x = random.randint(self.point_radius, self.image_size - self.point_radius)
            y = random.randint(self.point_radius, self.image_size - self.point_radius)
            cv2.circle(img, (x, y), self.point_radius, (0, 0, 255), -1)

        # 保存图像
        img_path = os.path.join(self.output_dir, f"{img_idx:04d}.jpg")
        cv2.imwrite(img_path, img)

        # 生成并保存JSON标注文件
        json_data = self._generate_json_annotation(img_idx, dashed_lines, drills)
        json_path = os.path.join(self.output_dir, f"{img_idx:04d}.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)

        return img

    def generate_dataset(self, num_images: int):
        """生成完整数据集"""
        for i in range(num_images):
            self.generate_image(i)
            print(f"生成图像 {i + 1}/{num_images}")

        print(f"数据集生成完成！保存在 {self.output_dir}")
        print("\n数据集结构:")
        print(f"├── 0000.jpg")
        print(f"├── 0000.json")
        print(f"├── 0001.jpg")
        print(f"├── 0001.json")
        print(f"└── ...")
        print(f"\n标注说明:")
        print(f"- 虚线段标注为 'line' (shape_type: line)")
        print(f"- 钻孔标注为 'drill' (shape_type: rectangle)")


# 使用示例
if __name__ == "__main__":
    # 创建生成器实例
    generator = YoloLinePointDatasetGenerator(
        output_dir=r"D:\PCITC\SH_project_data\线段检测\generated_lines_and_dots",
        image_size=1024,
        min_lines=5,
        max_lines=8,
        min_dashed=5,
        max_dashed=8,
        min_drills=3,
        max_drills=6,
        min_points=15,
        max_points=20,
        point_radius=4,
        line_width=2,
        drill_radius_range=(15, 30),
        point_on_line_ratio=0.5  # 50%的点在线上
    )

    # 生成100张图像的数据集
    generator.generate_dataset(100)