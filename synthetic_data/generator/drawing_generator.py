from abc import ABC
import numpy as np
import cv2
from typing import List, <PERSON><PERSON>, Union
import math
from PIL import Image, ImageDraw, ImageFont

from meta_type import Point, BoundingBox, Line, LineType, Circle, CircleFillPattern, Text
from settings import DATA


class DrawingElemGenerator(ABC):

    def __init__(self, image_size: Tuple[int, int] = (1920, 1080), **kwargs):
        super().__init__(**kwargs)
        img_width, img_height = image_size
        self.image_size = image_size
        self.image = Image.new('RGB', (img_width, img_height), 'white')
        self.draw = ImageDraw.Draw(self.image)

    def check_polygon_overlap(self, polygon1: List[Point], polygon2: List[Point], threshold: float = 0.1) -> bool:
        """Check if two polygons overlap.

        Args:
            polygon1: List of points representing the first polygon
            polygon2: List of points representing the second polygon
            threshold: Overlap threshold (0.0 to 1.0)

        Returns:
            bool: True if polygons overlap more than threshold, False otherwise
        """
        # Convert polygons to numpy arrays
        poly1 = np.array([[p.x, p.y] for p in polygon1], dtype=np.int32)
        poly2 = np.array([[p.x, p.y] for p in polygon2], dtype=np.int32)

        # Create blank images
        img1 = np.zeros(self.image_size, dtype=np.uint8)
        img2 = np.zeros(self.image_size, dtype=np.uint8)

        # Draw polygons
        cv2.fillPoly(img1, [poly1], 255)
        cv2.fillPoly(img2, [poly2], 255)

        # Calculate intersection
        intersection = cv2.bitwise_and(img1, img2)

        # Calculate areas
        area1 = cv2.countNonZero(img1)
        area2 = cv2.countNonZero(img2)
        intersection_area = cv2.countNonZero(intersection)

        # Calculate overlap ratio
        min_area = min(area1, area2)
        if min_area == 0:
            return False

        overlap_ratio = intersection_area / min_area
        return overlap_ratio > threshold

    def get_font(self, size, is_english: Union[bool, str] = False) -> ImageFont.FreeTypeFont:
        if isinstance(is_english, bool):
            if is_english:
                return ImageFont.truetype(DATA / "fonts/arial.ttf", size)
            else:
                return ImageFont.truetype(DATA / "fonts/simhei.ttf", size)
        else:
            if is_english == 'roman':
                return ImageFont.truetype(DATA / "fonts/times.ttf", size)
            else:
                raise ValueError('Not supported font type: {}'.format(is_english))

    def draw_line(self, point1: Point, point2: Point, line_type: LineType = LineType.LINE,
                  color: Tuple[int, int, int] = (0, 0, 0), thickness: int = 2) -> Line:
        """Draw a line with specified type.

        Args:
            point1: Start point coordinates
            point2: End point coordinates
            line_type: Type of line to draw (LineType.LINE, LineType.DASHED, LineType.LSS_DASHED)
            color: Line color as RGB tuple
            thickness: Line thickness
        """
        (x1, y1), (x2, y2) = point1.xy, point2.xy

        if line_type == LineType.LINE:
            self.draw.line([(x1, y1), (x2, y2)], fill=color, width=thickness)
        elif line_type in [LineType.DASHED, LineType.LSS_DASHED]:
            # Calculate line length and angle
            length = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
            if length == 0:
                raise ValueError("Start and end points cannot be the same.")

            # Unit direction vector
            dx = (x2 - x1) / length
            dy = (y2 - y1) / length

            if line_type == LineType.DASHED:
                # Equal length dashes
                dash_length = 20
                gap_length = 10
                current_length = 0

                while current_length < length:
                    # Start point of current dash
                    start_x = x1 + dx * current_length
                    start_y = y1 + dy * current_length

                    # End point of current dash
                    end_length = min(current_length + dash_length, length)
                    end_x = x1 + dx * end_length
                    end_y = y1 + dy * end_length

                    # Draw dash
                    self.draw.line([(int(start_x), int(start_y)), (int(end_x), int(end_y))],
                                   fill=color, width=thickness)

                    # Move to next dash
                    current_length += dash_length + gap_length

            else:  # lss_dashed
                # Long-short-short pattern
                long_dash = 30
                short_dash = 3
                gap = 10
                current_length = 0
                pattern_index = 0

                while current_length < length:
                    # Determine current dash length based on pattern
                    if pattern_index % 3 == 0:
                        dash_length = long_dash
                    else:
                        dash_length = short_dash

                    # Start point of current dash
                    start_x = x1 + dx * current_length
                    start_y = y1 + dy * current_length

                    # End point of current dash
                    end_length = min(current_length + dash_length, length)
                    end_x = x1 + dx * end_length
                    end_y = y1 + dy * end_length

                    # Draw dash
                    self.draw.line([(int(start_x), int(start_y)), (int(end_x), int(end_y))],
                                   fill=color, width=thickness)

                    # Move to next dash
                    current_length += dash_length + gap
                    pattern_index += 1
        else:
            raise ValueError(f"Invalid line type: {line_type}")

        return Line(x1, y1, x2, y2, line_type)

    def _draw_triangle_in_circle(self, center: Point, radius: int, color: Tuple[int, int, int]) -> None:
        """Draw a triangle inside a circle."""
        points = []
        for i in range(3):
            angle = i * 2 * np.pi / 3
            points.append((
                int(center.x + radius * 0.7 * np.cos(angle)),
                int(center.y + radius * 0.7 * np.sin(angle))
            ))
        self.draw.polygon(points, fill=color)

    def _draw_semicircle_in_circle(self, center: Point, radius: int, color: Tuple[int, int, int]) -> None:
        """Draw a semicircle inside a circle."""
        bbox = [
            int(center.x - radius * 0.7),
            int(center.y - radius * 0.7),
            int(center.x + radius * 0.7),
            int(center.y + radius * 0.7)
        ]
        self.draw.pieslice(bbox, 0, 180, fill=color)

    def _draw_star_in_circle(self, center: Point, radius: int, color: Tuple[int, int, int]) -> None:
        """Draw a star inside a circle."""
        points = []
        for i in range(10):
            angle = i * np.pi / 5
            r = radius * 0.7 if i % 2 == 0 else radius * 0.3
            points.append((
                int(center.x + r * np.cos(angle)),
                int(center.y + r * np.sin(angle))
            ))
        self.draw.polygon(points, fill=color)

    def _draw_rectangle_in_circle(self, center: Point, radius: int, color: Tuple[int, int, int]) -> None:
        """Draw a rectangle inside a circle."""
        size = int(radius * 0.7)
        bbox = [
            int(center.x - size),
            int(center.y - size),
            int(center.x + size),
            int(center.y + size)
        ]
        self.draw.rectangle(bbox, fill=color)

    def draw_circle(self, center: Point, radius: int, fill_pattern: CircleFillPattern = CircleFillPattern.NONE,
                    color: Tuple[int, int, int] = (0, 0, 0), thickness: int = 2) -> Circle:
        """Draw a circle with optional fill pattern."""
        bbox = [
            int(center.x - radius),
            int(center.y - radius),
            int(center.x + radius),
            int(center.y + radius)
        ]
        self.draw.ellipse(bbox, outline=color, width=thickness)

        if fill_pattern:
            if fill_pattern == CircleFillPattern.TRIANGLE:
                self._draw_triangle_in_circle(center, radius, color)
            elif fill_pattern == CircleFillPattern.SEMICIRCLE:
                self._draw_semicircle_in_circle(center, radius, color)
            elif fill_pattern == CircleFillPattern.STAR:
                self._draw_star_in_circle(center, radius, color)
            elif fill_pattern == CircleFillPattern.RECTANGLE:
                self._draw_rectangle_in_circle(center, radius, color)

        return Circle(center.x, center.y, radius, fill_pattern)

    def draw_text(self, text: str, position: Point, angle: float = 0, spacing: float = 4,
                  color: Tuple[int, int, int] = (0, 0, 0), font_scale: float = 1.0, is_english: Union[bool, str] = False) -> Text:
        """Draw text with rotation and custom character spacing.

        Args:
            text: Text to draw
            position: Center position of the text
            angle: Rotation angle in degrees (positive = clockwise)
            spacing: Spacing between characters (pixels)
            color: Text color
            font_scale: Font size scale factor
            is_english: Whether the text is in English (uses different font)
        """
        # 创建字体
        font_size = int(32 * font_scale)
        font = self.get_font(font_size, is_english=is_english)

        # 手动计算文本整体宽度和最大高度
        bbox = self.draw.textbbox((0, 0), text, font=font)
        char_widths = [font.getbbox(c)[2] - font.getbbox(c)[0] for c in text]
        text_width = sum(char_widths) + spacing * (len(text) - 1)
        text_height = max(font.getbbox(c)[3] - font.getbbox(c)[1] for c in text)

        # 创建透明临时图像
        text_img = Image.new('RGBA', (text_width, text_height), (255, 255, 255, 0))
        text_draw = ImageDraw.Draw(text_img)

        # 按字符绘制，每个字之间加 spacing
        if spacing > 4:
            x = 0
            for i, c in enumerate(text):
                text_draw.text((x, -font.getbbox(c)[1]), c, font=font, fill=color)
                x += char_widths[i] + spacing
        else:
            text_draw.text((-bbox[0], -bbox[1]), text, font=font, fill=color)

        # 旋转临时图像
        rotated_text = text_img.rotate(-angle, expand=True, resample=Image.BICUBIC)

        # 计算粘贴位置
        rotated_width, rotated_height = rotated_text.size
        paste_x = int(position.x - rotated_width / 2)
        paste_y = int(position.y - rotated_height / 2)

        # 粘贴到主图像
        self.image.paste(rotated_text, (paste_x, paste_y), rotated_text)

        # 计算边界框
        bbox = BoundingBox(
            paste_x,
            paste_y,
            paste_x + rotated_width,
            paste_y + rotated_height
        )

        return Text(text, font_scale, bbox, angle)

    def clear(self) -> None:
        """Clear the current image and annotations."""
        self.image = Image.new('RGB', (self.image_size[0], self.image_size[1]), 'white')
        self.draw = ImageDraw.Draw(self.image)
